package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleLocalDateTimeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;

/**
 * Jackson配置类
 * 解决LocalDateTime反序列化问题
 * 
 * 使用现有的FlexibleLocalDateTimeDeserializer，支持多种日期格式：
 * - ISO格式：yyyy-MM-dd'T'HH:mm:ss
 * - 空格格式：yyyy-MM-dd HH:mm:ss (Cash-Manage模块使用的格式)
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        
        // 使用现有的灵活LocalDateTime反序列化器
        // 支持 "yyyy-MM-dd'T'HH:mm:ss" 和 "yyyy-MM-dd HH:mm:ss" 两种格式
        javaTimeModule.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());
        
        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        return mapper;
    }
}
