# LocalDateTime反序列化问题快速修复指南

## 问题现象
```
feign.codec.DecodeException: Error while extracting response for type [class com.jinghang.cash.api.dto.ProjectInfoDto]
Caused by: Cannot deserialize value of type `java.time.LocalDateTime` from String "2025-08-21 17:09:00"
```

## 快速修复步骤（推荐方案）

### 1. 在Flow模块创建Jackson配置类

文件路径：`src/main/java/com/maguo/loan/cash/flow/config/JacksonConfig.java`

**使用现有的FlexibleLocalDateTimeDeserializer（推荐）**：

```java
package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleLocalDateTimeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;

/**
 * Jackson配置类 - 解决LocalDateTime反序列化问题
 * 使用现有的FlexibleLocalDateTimeDeserializer，支持多种格式
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        JavaTimeModule javaTimeModule = new JavaTimeModule();
        // 使用现有的灵活反序列化器，支持多种日期格式
        javaTimeModule.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }
}
```

**优势**：
- 复用现有代码，影响最小
- 支持多种日期格式（ISO格式和空格格式）
- 更灵活，向后兼容性更好

### 2. 重启Flow服务

### 3. 验证修复效果

调用接口：`POST /queryInfo/LXCJCY-RD001`

确认不再出现LocalDateTime反序列化错误。

## 原因说明

- Cash-Manage模块使用FastJSON，日期格式为`"yyyy-MM-dd HH:mm:ss"`
- Flow模块使用Jackson，需要配置相同的日期格式才能正确反序列化
