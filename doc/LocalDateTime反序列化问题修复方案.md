# LocalDateTime反序列化问题修复方案

## 问题描述

Flow模块调用Cash-Manage模块的项目信息接口时，出现LocalDateTime反序列化错误：

```
Cannot deserialize value of type `java.time.LocalDateTime` from String "2025-08-21 17:09:00":
Failed to deserialize java.time.LocalDateTime: (java.time.format.DateTimeParseException)
Text '2025-08-21 17:09:00' could not be parsed at index 10
```

## 问题根因分析

1. **Cash-Manage模块**：使用FastJSON序列化，配置了日期格式为`"yyyy-MM-dd HH:mm:ss"`
2. **Flow模块**：使用Jackson反序列化，但没有配置相应的LocalDateTime格式
3. **数据流向**：Cash-Manage → JSON(`"2025-08-21 17:09:00"`) → Flow模块Jackson反序列化失败

## 解决方案

### 方案1：在Flow模块添加Jackson配置类（推荐）

在Flow模块的`src/main/java/com/maguo/loan/cash/flow/config/`目录下创建`JacksonConfig.java`：

```java
package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 配置LocalDateTime反序列化器，支持"yyyy-MM-dd HH:mm:ss"格式
        javaTimeModule.addDeserializer(LocalDateTime.class,
            new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }
}
```

### 方案2：使用现有的FlexibleLocalDateTimeDeserializer

Flow模块已经有了`FlexibleLocalDateTimeDeserializer`类，可以直接使用：

```java
package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleLocalDateTimeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;

@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 使用现有的灵活LocalDateTime反序列化器
        javaTimeModule.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }
}
```

### 方案3：配置Feign客户端使用自定义ObjectMapper

在Flow模块创建Feign配置类：

```java
package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import feign.codec.Decoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Configuration
public class FeignConfig {

    @Bean
    public Decoder feignDecoder() {
        ObjectMapper objectMapper = new ObjectMapper();
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addDeserializer(LocalDateTime.class,
            new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(objectMapper);
        return new SpringDecoder(() -> List.of(converter));
    }
}
```

## 实施步骤

1. 在Flow模块创建Jackson配置类（推荐使用方案1）
2. 重启Flow服务
3. 测试项目信息查询接口验证修复效果

## 影响范围

- 仅影响Flow模块的Jackson反序列化配置
- 不影响Cash-Manage模块的现有配置
- 向后兼容，不会影响其他功能

## 验证方法

修复后，可以通过以下方式验证：

1. 调用Flow模块的项目信息查询接口：`POST /queryInfo/LXCJCY-RD001`
2. 检查日志确认不再出现LocalDateTime反序列化错误
3. 确认返回的项目信息中包含正确的时间字段

## 快速修复代码

在Flow模块的`src/main/java/com/maguo/loan/cash/flow/config/`目录下创建`JacksonConfig.java`：

```java
package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Jackson配置类
 * 解决LocalDateTime反序列化问题
 */
@Configuration
public class JacksonConfig {

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 配置LocalDateTime序列化和反序列化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }
}
```

## 注意事项

1. **配置优先级**：使用`@Primary`注解确保自定义的ObjectMapper优先使用
2. **格式一致性**：确保日期格式与Cash-Manage模块的FastJSON配置一致
3. **向后兼容**：该配置不会影响其他已有功能
4. **重启服务**：配置修改后需要重启Flow服务才能生效
